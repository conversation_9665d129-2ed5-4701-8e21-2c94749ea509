<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>结果录入测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .form-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }
        .result-checkbox {
            display: inline-block;
            margin-right: 15px;
        }
        .result-checkbox input {
            width: auto;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>结果录入测试</h1>
        
        <div class="form-group">
            <label for="serverUrl">服务器地址:</label>
            <input type="text" id="serverUrl" value="ws://localhost:8080/ws" placeholder="WebSocket服务器地址">
        </div>
        
        <div class="form-group">
            <button onclick="connect()">连接</button>
            <button onclick="disconnect()">断开连接</button>
            <button onclick="login()">登录</button>
        </div>
        
        <div id="status" class="status disconnected">未连接</div>
    </div>

    <div class="container">
        <h2>1. 先发送下注记录</h2>
        
        <div class="form-row">
            <div class="form-group">
                <label>账期:</label>
                <input type="text" id="betAccountPeriod" value="********" placeholder="YYYYMMDD">
            </div>
            <div class="form-group">
                <label>场次编号:</label>
                <input type="number" id="betRoundNo" value="1">
            </div>
            <div class="form-group">
                <label>局号编号:</label>
                <input type="number" id="betHandNo" value="1">
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label>洗码号:</label>
                <input type="text" id="betWashCode" value="TEST001" placeholder="洗码号">
            </div>
            <div class="form-group">
                <label>客户姓名:</label>
                <input type="text" id="betUserName" value="测试用户" placeholder="客户姓名">
            </div>
            <div class="form-group">
                <label>货币类型:</label>
                <select id="betCurrencyType">
                    <option value="1">筹码</option>
                    <option value="2">现金</option>
                    <option value="3">U码</option>
                </select>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label>庄金额:</label>
                <input type="number" id="betBankerAmount" value="1000" step="0.01">
            </div>
            <div class="form-group">
                <label>闲金额:</label>
                <input type="number" id="betPlayerAmount" value="0" step="0.01">
            </div>
            <div class="form-group">
                <label>和金额:</label>
                <input type="number" id="betTieAmount" value="0" step="0.01">
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label>庄对金额:</label>
                <input type="number" id="betBankerPairAmount" value="0" step="0.01">
            </div>
            <div class="form-group">
                <label>闲对金额:</label>
                <input type="number" id="betPlayerPairAmount" value="0" step="0.01">
            </div>
            <div class="form-group">
                <label>和底:</label>
                <input type="number" id="betAmountBottom" value="0" step="0.01">
            </div>
        </div>
        
        <button onclick="sendBet()">发送下注记录</button>
    </div>

    <div class="container">
        <h2>2. 录入结果并结算</h2>
        
        <div class="form-row">
            <div class="form-group">
                <label>账期:</label>
                <input type="text" id="resultAccountPeriod" value="********" placeholder="YYYYMMDD">
            </div>
            <div class="form-group">
                <label>场次编号:</label>
                <input type="number" id="resultRoundNo" value="1">
            </div>
            <div class="form-group">
                <label>局号编号:</label>
                <input type="number" id="resultHandNo" value="1">
            </div>
        </div>
        
        <div class="form-group">
            <label>游戏结果（可多选）:</label>
            <div>
                <div class="result-checkbox">
                    <input type="checkbox" id="result_banker" value="庄">
                    <label for="result_banker">庄</label>
                </div>
                <div class="result-checkbox">
                    <input type="checkbox" id="result_player" value="闲">
                    <label for="result_player">闲</label>
                </div>
                <div class="result-checkbox">
                    <input type="checkbox" id="result_tie" value="和">
                    <label for="result_tie">和</label>
                </div>
                <div class="result-checkbox">
                    <input type="checkbox" id="result_banker_pair" value="庄对">
                    <label for="result_banker_pair">庄对</label>
                </div>
                <div class="result-checkbox">
                    <input type="checkbox" id="result_player_pair" value="闲对">
                    <label for="result_player_pair">闲对</label>
                </div>
                <div class="result-checkbox">
                    <input type="checkbox" id="result_lucky6" value="幸运6">
                    <label for="result_lucky6">幸运6</label>
                </div>
                <div class="result-checkbox">
                    <input type="checkbox" id="result_lucky7" value="幸运7">
                    <label for="result_lucky7">幸运7</label>
                </div>
            </div>
        </div>
        
        <button onclick="sendResult()">录入结果并结算</button>
        <button onclick="queryBetRecords()">查询下注记录</button>
    </div>

    <div class="container">
        <h2>消息日志</h2>
        <div id="log" class="log"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <script>
        let ws = null;
        let isConnected = false;

        function updateStatus(message, connected) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = connected ? 'status connected' : 'status disconnected';
            isConnected = connected;
        }

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function connect() {
            const serverUrl = document.getElementById('serverUrl').value;
            
            if (ws) {
                ws.close();
            }
            
            ws = new WebSocket(serverUrl);
            
            ws.onopen = function(event) {
                updateStatus('已连接', true);
                log('WebSocket连接已建立');
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                log(`收到消息: ${JSON.stringify(message, null, 2)}`);
            };
            
            ws.onclose = function(event) {
                updateStatus('连接已断开', false);
                log('WebSocket连接已断开');
            };
            
            ws.onerror = function(error) {
                updateStatus('连接错误', false);
                log('WebSocket错误: ' + error);
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function login() {
            if (!isConnected) {
                alert('请先连接到服务器');
                return;
            }
            
            const loginMessage = {
                type: 'login',
                data: {
                    username: 'admin',
                    password: 'admin123'
                }
            };
            
            ws.send(JSON.stringify(loginMessage));
            log('发送登录请求');
        }

        function sendBet() {
            if (!isConnected) {
                alert('请先连接到服务器');
                return;
            }
            
            const betRecord = {
                game_type: 1, // 百家乐
                account_period: document.getElementById('betAccountPeriod').value,
                round_no: parseInt(document.getElementById('betRoundNo').value),
                hand_no: parseInt(document.getElementById('betHandNo').value),
                wash_code: document.getElementById('betWashCode').value,
                user_name: document.getElementById('betUserName').value,
                currency_type: parseInt(document.getElementById('betCurrencyType').value),
                banker_amount: parseFloat(document.getElementById('betBankerAmount').value) || 0,
                player_amount: parseFloat(document.getElementById('betPlayerAmount').value) || 0,
                tie_amount: parseFloat(document.getElementById('betTieAmount').value) || 0,
                banker_pair_amount: parseFloat(document.getElementById('betBankerPairAmount').value) || 0,
                player_pair_amount: parseFloat(document.getElementById('betPlayerPairAmount').value) || 0,
                lucky_6_amount: 0,
                lucky_7_amount: 0,
                amount_bottom: parseFloat(document.getElementById('betAmountBottom').value) || 0
            };
            
            const message = {
                type: 'batch_bet_entry',
                data: {
                    bet_records: [betRecord]
                }
            };
            
            ws.send(JSON.stringify(message));
            log('发送下注记录');
        }

        function sendResult() {
            if (!isConnected) {
                alert('请先连接到服务器');
                return;
            }
            
            // 获取选中的结果
            const results = [];
            const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
            checkboxes.forEach(checkbox => {
                results.push(checkbox.value);
            });
            
            if (results.length === 0) {
                alert('请选择至少一个游戏结果');
                return;
            }
            
            const message = {
                type: 'enter_result',
                data: {
                    account_period: document.getElementById('resultAccountPeriod').value,
                    round_no: parseInt(document.getElementById('resultRoundNo').value),
                    hand_no: parseInt(document.getElementById('resultHandNo').value),
                    result: results
                }
            };
            
            ws.send(JSON.stringify(message));
            log(`发送结果录入请求，结果: ${results.join(', ')}`);
        }

        function queryBetRecords() {
            if (!isConnected) {
                alert('请先连接到服务器');
                return;
            }
            
            const message = {
                type: 'query_bet_records',
                data: {
                    account_period: document.getElementById('resultAccountPeriod').value,
                    round_no: parseInt(document.getElementById('resultRoundNo').value),
                    hand_no: parseInt(document.getElementById('resultHandNo').value)
                }
            };
            
            ws.send(JSON.stringify(message));
            log('发送查询下注记录请求');
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
    </script>
</body>
</html>
