<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket API 接口清单</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .category {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        .category-header {
            background-color: #007bff;
            color: white;
            padding: 15px;
            font-size: 18px;
            font-weight: bold;
        }
        .api-item {
            border-bottom: 1px solid #eee;
            padding: 15px;
        }
        .api-item:last-child {
            border-bottom: none;
        }
        .api-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .api-type {
            display: inline-block;
            background-color: #28a745;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            margin-left: 10px;
        }
        .api-description {
            color: #666;
            margin: 5px 0;
        }
        .api-params {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .status-required {
            background-color: #dc3545;
        }
        .status-optional {
            background-color: #ffc107;
            color: #333;
        }
        .status-no-params {
            background-color: #6c757d;
        }
        .back-to-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #007bff;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            text-decoration: none;
            cursor: pointer;
        }
        .toc {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .toc h3 {
            margin-top: 0;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 5px 0;
        }
        .toc a {
            text-decoration: none;
            color: #007bff;
        }
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket API 接口清单</h1>
        
        <div class="toc">
            <h3>目录</h3>
            <ul>
                <li><a href="#auth">用户认证接口</a></li>
                <li><a href="#table">桌台信息接口</a></li>
                <li><a href="#shuffle">洗牌开场接口</a></li>
                <li><a href="#finance">财务接口</a></li>
                <li><a href="#bet">下注接口</a></li>
                <li><a href="#settlement">结算接口</a></li>
                <li><a href="#system">系统接口</a></li>
                <li><a href="#utility">工具接口</a></li>
                <li><a href="#notifications">🚨 主动推送接口</a></li>
            </ul>
        </div>

        <!-- 用户认证接口 -->
        <div class="category" id="auth">
            <div class="category-header">用户认证接口</div>
            
            <div class="api-item">
                <div class="api-name">用户登录 <span class="api-type status-required">需要参数</span></div>
                <div class="api-description">用户账号密码登录，返回JWT令牌</div>
                <div class="api-params">
                    <strong>消息类型：</strong>login<br>
                    <strong>参数：</strong>username, password
                </div>
            </div>

            <div class="api-item">
                <div class="api-name">用户登出 <span class="api-type status-no-params">无需参数</span></div>
                <div class="api-description">用户登出，清除认证状态</div>
                <div class="api-params">
                    <strong>消息类型：</strong>logout<br>
                    <strong>参数：</strong>无
                </div>
            </div>

            <div class="api-item">
                <div class="api-name">获取用户信息 <span class="api-type status-no-params">无需参数</span></div>
                <div class="api-description">获取当前登录用户的详细信息</div>
                <div class="api-params">
                    <strong>消息类型：</strong>get_user_info<br>
                    <strong>参数：</strong>无
                </div>
            </div>

            <div class="api-item">
                <div class="api-name">修改密码 <span class="api-type status-required">需要参数</span></div>
                <div class="api-description">修改当前用户密码</div>
                <div class="api-params">
                    <strong>消息类型：</strong>change_password<br>
                    <strong>参数：</strong>old_password, new_password
                </div>
            </div>

            <div class="api-item">
                <div class="api-name">验证令牌 <span class="api-type status-required">需要参数</span></div>
                <div class="api-description">验证JWT令牌的有效性</div>
                <div class="api-params">
                    <strong>消息类型：</strong>verify_token<br>
                    <strong>参数：</strong>token
                </div>
            </div>
        </div>

        <!-- 桌台信息接口 -->
        <div class="category" id="table">
            <div class="category-header">桌台信息接口</div>
            
            <div class="api-item">
                <div class="api-name">获取桌台信息 <span class="api-type status-no-params">无需参数</span></div>
                <div class="api-description">根据客户端IP地址自动获取对应的桌台信息</div>
                <div class="api-params">
                    <strong>消息类型：</strong>get_table_info<br>
                    <strong>参数：</strong>无（自动通过IP获取）
                </div>
            </div>

            <div class="api-item">
                <div class="api-name">获取最新开台数据 <span class="api-type status-no-params">无需参数</span></div>
                <div class="api-description">通过客户端IP自动获取对应桌台的最新开台数据</div>
                <div class="api-params">
                    <strong>消息类型：</strong>get_latest_tables_start<br>
                    <strong>参数：</strong>无（自动通过IP获取）
                </div>
            </div>

            <div class="api-item">
                <div class="api-name">获取桌台情况统计 <span class="api-type status-required">需要参数</span></div>
                <div class="api-description">获取指定账期的桌台统计信息，包括洗牌信息、合并统计（财务统计+手牌统计，按货币类型分组）</div>
                <div class="api-params">
                    <strong>消息类型：</strong>get_table_stats<br>
                    <strong>参数：</strong>account_period (账期，格式：YYYYMMDD)<br>
                    <strong>测试页面：</strong><a href="test_table_stats_api.html" target="_blank">桌台统计接口测试</a>
                </div>
            </div>

            <div class="api-item">
                <div class="api-name">获取条口记录结果（露珠图数据） <span class="api-type status-required">需要参数</span></div>
                <div class="api-description">查询当前桌台当前账期所有场次局数的游戏结果，用于前端生成露珠图</div>
                <div class="api-params">
                    <strong>消息类型：</strong>get_hand_records_result<br>
                    <strong>参数：</strong>account_period（账期，YYYYMMDD格式）<br>
                    <strong>测试页面：</strong><a href="test_hand_records_result_api.html" target="_blank">测试条口记录结果接口</a>
                </div>
            </div>

            <div class="api-item">
                <div class="api-name">收盘功能 <span class="api-type status-required">需要参数</span></div>
                <div class="api-description">将当前桌台最新的账期记录状态改为已收盘，并创建新的账期记录</div>
                <div class="api-params">
                    <strong>消息类型：</strong>close_table<br>
                    <strong>参数：</strong>table_id
                </div>
            </div>
        </div>

        <!-- 洗牌开场接口 -->
        <div class="category" id="shuffle">
            <div class="category-header">洗牌开场接口</div>
            
            <div class="api-item">
                <div class="api-name">洗牌开场 <span class="api-type status-required">需要参数</span></div>
                <div class="api-description">开始洗牌并记录洗牌信息，同时更新桌台账期状态</div>
                <div class="api-params">
                    <strong>消息类型：</strong>start_shuffle<br>
                    <strong>参数：</strong>table_id, account_period, shift, shuffle_method, card_color, monitor_id, monitor_name, admin_id, admin_name, shuffle_table_poker, table_poker, monitor_poker, cut_card_dealer
                </div>
            </div>
        </div>

        <!-- 财务接口 -->
        <div class="category" id="finance">
            <div class="category-header">财务接口</div>
            
            <div class="api-item">
                <div class="api-name">申请出码/加彩 <span class="api-type status-required">需要参数</span></div>
                <div class="api-description">批量提交出码或加彩申请，支持同时申请多种货币类型。出码状态直接为同意，加彩状态为申请中</div>
                <div class="api-params">
                    <strong>消息类型：</strong>apply_out_code<br>
                    <strong>参数：</strong>operation_type, account_period, amounts[]<br>
                    <strong>说明：</strong>operation_type: 1-出码, 3-加彩; 桌台ID通过IP自动获取
                </div>
            </div>

            <div class="api-item">
                <div class="api-name">获取出码申请列表 <span class="api-type status-optional">可选参数</span></div>
                <div class="api-description">获取出码申请列表，支持分页和筛选</div>
                <div class="api-params">
                    <strong>消息类型：</strong>get_out_code_list<br>
                    <strong>参数：</strong>table_id, status, limit, offset
                </div>
            </div>

            <div class="api-item">
                <div class="api-name">获取出码申请详情 <span class="api-type status-required">需要参数</span></div>
                <div class="api-description">根据ID获取出码申请详情</div>
                <div class="api-params">
                    <strong>消息类型：</strong>get_out_code_detail<br>
                    <strong>参数：</strong>id
                </div>
            </div>

            <div class="api-item">
                <div class="api-name">点码查询 <span class="api-type status-required">需要参数</span></div>
                <div class="api-description">查询指定桌台和账期的点码汇总信息</div>
                <div class="api-params">
                    <strong>消息类型：</strong>get_code_summary<br>
                    <strong>参数：</strong>table_id, account_period
                </div>
            </div>
        </div>

        <!-- 下注接口 -->
        <div class="category" id="bet">
            <div class="category-header">下注接口</div>
            
            <div class="api-item">
                <div class="api-name">实时检查下注限制（推荐） <span class="api-type status-required">需要参数</span></div>
                <div class="api-description">实时检查客户端输入的下注金额是否超出限制，用于在录入时立即提示用户</div>
                <div class="api-params">
                    <strong>消息类型：</strong>check_bet_limits<br>
                    <strong>参数：</strong>currency_type, banker_amount, player_amount, tie_amount, banker_pair_amount, player_pair_amount, lucky_6_amount, lucky_7_amount<br>
                    <strong>特点：</strong>⚡ 实时检查 📋 详细信息 🎯 精准提示 🚫 不影响流程<br>
                    <strong>测试页面：</strong><a href="test_check_bet_limits.html" target="_blank">实时下注限制检查测试</a>
                </div>
            </div>


            <div class="api-item">
                <div class="api-name">批量下注录入 <span class="api-type status-required">需要参数</span></div>
                <div class="api-description">批量下注记录保存，支持多个用户的下注记录</div>
                <div class="api-params">
                    <strong>消息类型：</strong>batch_bet_entry<br>
                    <strong>参数：</strong>bet_records[] (包含洗码号、货币类型、下注金额等)<br>
                    <strong>测试页面：</strong><a href="test_batch_bet.html" target="_blank">批量下注测试</a>
                </div>
            </div>

            <div class="api-item">
                <div class="api-name">百家乐结果录入 <span class="api-type status-required">需要参数</span></div>
                <div class="api-description">录入百家乐游戏结果并进行结算</div>
                <div class="api-params">
                    <strong>消息类型：</strong>enter_result<br>
                    <strong>参数：</strong>table_id, account_period, round_no, hand_no, result[]
                </div>
            </div>
        </div>

        <!-- 结算接口 -->
        <div class="category" id="settlement">
            <div class="category-header">结算接口</div>
            
            <div class="api-item">
                <div class="api-name">批量点码 <span class="api-type status-required">需要参数</span></div>
                <div class="api-description">批量保存点码记录，支持多种货币类型同时点码</div>
                <div class="api-params">
                    <strong>消息类型：</strong>batch_settlement<br>
                    <strong>参数：</strong>settlement_records[] (包含table_id, shoe_no, account_period, currency_type等)
                </div>
            </div>
        </div>

        <!-- 系统接口 -->
        <div class="category" id="system">
            <div class="category-header">系统接口</div>
            
            <div class="api-item">
                <div class="api-name">获取系统用户信息 <span class="api-type status-required">需要参数</span></div>
                <div class="api-description">根据员工编号获取系统用户信息</div>
                <div class="api-params">
                    <strong>消息类型：</strong>get_sys_user_by_serial<br>
                    <strong>参数：</strong>serial_number
                </div>
            </div>

            <div class="api-item">
                <div class="api-name">获取客户信息 <span class="api-type status-required">需要参数</span></div>
                <div class="api-description">根据洗码号获取客户信息</div>
                <div class="api-params">
                    <strong>消息类型：</strong>get_user_by_wash_code<br>
                    <strong>参数：</strong>wash_code
                </div>
            </div>
        </div>

        <!-- 工具接口 -->
        <div class="category" id="utility">
            <div class="category-header">工具接口</div>
            
            <div class="api-item">
                <div class="api-name">心跳检测 <span class="api-type status-no-params">无需参数</span></div>
                <div class="api-description">客户端发送心跳保持连接</div>
                <div class="api-params">
                    <strong>消息类型：</strong>ping<br>
                    <strong>参数：</strong>无
                </div>
            </div>

            <div class="api-item">
                <div class="api-name">广播消息 <span class="api-type status-required">需要参数</span></div>
                <div class="api-description">向所有连接的客户端广播消息</div>
                <div class="api-params">
                    <strong>消息类型：</strong>broadcast<br>
                    <strong>参数：</strong>message
                </div>
            </div>

            <div class="api-item">
                <div class="api-name">私聊消息 <span class="api-type status-required">需要参数</span></div>
                <div class="api-description">向指定客户端发送私聊消息</div>
                <div class="api-params">
                    <strong>消息类型：</strong>private<br>
                    <strong>参数：</strong>to, message
                </div>
            </div>

            <div class="api-item">
                <div class="api-name">获取客户端列表 <span class="api-type status-no-params">无需参数</span></div>
                <div class="api-description">获取当前连接的所有客户端信息</div>
                <div class="api-params">
                    <strong>消息类型：</strong>get_clients<br>
                    <strong>参数：</strong>无
                </div>
            </div>
        </div>

        <!-- 主动推送接口 -->
        <div class="category" id="notifications">
            <div class="category-header">🚨 主动推送接口</div>
            
            <div class="api-item">
                <div class="api-name">下注超限提示 <span class="api-type status-no-params">异步推送</span></div>
                <div class="api-description">当下注超出限制时，服务端异步推送超限提示给客户端（不阻止下注）</div>
                <div class="api-params">
                    <strong>消息类型：</strong>bet_limit_warning<br>
                    <strong>推送模式：</strong>异步提示（下注先保存，后检查限制）<br>
                    <strong>触发条件：</strong>下注金额超出桌台或区域限制<br>
                    <strong>推送内容：</strong>违规区域详情、超限金额、限制类型、建议信息<br>
                    <strong>测试页面：</strong><a href="test_bet_limit_notification.html" target="_blank">下注超限通知测试</a>
                </div>
            </div>
        </div>

        <div style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 5px;">
            <h3>接口统计</h3>
            <p><strong>总接口数量：</strong>27个</p>
            <p><strong>需要参数的接口：</strong>18个</p>
            <p><strong>无需参数的接口：</strong>9个</p>
            <p><strong>主动推送接口：</strong>1个</p>
            <p><strong>按功能分类：</strong></p>
            <ul>
                <li>用户认证接口：5个</li>
                <li>桌台信息接口：5个</li>
                <li>洗牌开场接口：1个</li>
                <li>财务接口：4个</li>
                <li>下注接口：5个（新增：实时限制检查）</li>
                <li>结算接口：1个</li>
                <li>系统接口：2个</li>
                <li>工具接口：4个</li>
                <li>主动推送接口：1个</li>
            </ul>
        </div>
    </div>

    <a href="#" class="back-to-top" onclick="window.scrollTo(0,0)">返回顶部</a>

    <script>
        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html> 