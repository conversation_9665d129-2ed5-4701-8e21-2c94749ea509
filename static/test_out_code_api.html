<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>出码申请接口测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status-panel {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected {
            background-color: #28a745;
        }
        .status-disconnected {
            background-color: #dc3545;
        }
        .control-panel {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
        }
        .section h2 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .form-inline {
            display: flex;
            gap: 10px;
            align-items: end;
        }
        .form-inline .form-group {
            flex: 1;
            margin-bottom: 0;
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            background-color: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            word-wrap: break-word;
        }
        .message-send {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .message-receive {
            background-color: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }
        .message-error {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
        }
        .message-success {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .timestamp {
            color: #666;
            font-size: 11px;
            margin-right: 10px;
        }
        .user-info {
            background-color: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
        }
        .user-info h3 {
            margin-top: 0;
            color: #2e7d32;
        }
        .currency-group {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f8f9fa;
        }
        .currency-group h4 {
            margin-top: 0;
            margin-bottom: 10px;
            color: #495057;
        }
        .checkbox-group {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 10px;
        }
        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>批量出码申请接口测试工具</h1>
        
        <!-- 连接状态 -->
        <div class="status-panel">
            <span class="status-indicator" id="status-indicator"></span>
            <span id="connection-status">未连接</span>
            <span style="margin-left: 20px;">客户端ID: <span id="client-id">-</span></span>
        </div>

        <!-- 连接控制 -->
        <div class="control-panel">
            <button class="btn btn-primary" onclick="connect()">连接</button>
            <button class="btn btn-danger" onclick="disconnect()">断开连接</button>
            <button class="btn btn-warning" onclick="clearMessages()">清空消息</button>
        </div>

        <!-- 用户登录 -->
        <div class="section">
            <h2>用户登录</h2>
            <div class="form-inline">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" value="admin" placeholder="请输入用户名">
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" value="123456" placeholder="请输入密码">
                </div>
                <div class="form-group">
                    <button class="btn btn-success" onclick="login()">登录</button>
                </div>
            </div>
            <div id="user-info-display"></div>
        </div>

        <!-- 批量出码申请 -->
        <div class="section">
            <h2>批量申请（出码/加彩）</h2>
            <div class="form-group">
                <label style="color: #666; font-size: 12px;">桌台ID将通过IP自动获取</label>
            </div>
            <div class="form-group">
                <label for="operation-type">操作类型</label>
                <select id="operation-type">
                    <option value="1">出码（状态直接为同意）</option>
                    <option value="3">加彩（状态为申请中）</option>
                </select>
            </div>
            <div class="form-group">
                <label for="out-account-period">账期 (YYYYMMDD格式)</label>
                <input type="text" id="out-account-period" placeholder="例如: ********">
            </div>
            
            <!-- 货币类型选择 -->
            <div class="currency-group">
                <h4>选择货币类型和金额</h4>
                <div class="checkbox-group">
                    <label>
                        <input type="checkbox" id="enable-chips" checked> 筹码
                    </label>
                    <label>
                        <input type="checkbox" id="enable-cash" checked> 现金
                    </label>
                    <label>
                        <input type="checkbox" id="enable-ucode" checked> U码
                    </label>
                </div>
                
                <div class="form-group" id="chips-amount-group">
                    <label for="chips-amount">筹码金额</label>
                    <input type="number" id="chips-amount" value="50000" step="0.01" placeholder="请输入筹码金额">
                </div>
                
                <div class="form-group" id="cash-amount-group">
                    <label for="cash-amount">现金金额</label>
                    <input type="number" id="cash-amount" value="30000" step="0.01" placeholder="请输入现金金额">
                </div>
                
                <div class="form-group" id="ucode-amount-group">
                    <label for="ucode-amount">U码金额</label>
                    <input type="number" id="ucode-amount" value="20000" step="0.01" placeholder="请输入U码金额">
                </div>
            </div>
            
            <button class="btn btn-success" onclick="applyOutCode()">提交申请</button>
        </div>

        <!-- 出码申请列表 -->
        <div class="section">
            <h2>出码申请列表</h2>
            <div class="form-inline">
                <div class="form-group">
                    <label for="list-table-id">桌台ID (可选)</label>
                    <input type="number" id="list-table-id" placeholder="桌台ID，0表示全部">
                </div>
                <div class="form-group">
                    <label for="list-status">状态 (可选)</label>
                    <select id="list-status">
                        <option value="0">全部状态</option>
                        <option value="1">申请中</option>
                        <option value="2">同意</option>
                        <option value="3">拒绝</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="list-limit">每页数量</label>
                    <input type="number" id="list-limit" value="20" min="1" max="100">
                </div>
                <div class="form-group">
                    <label for="list-offset">偏移量</label>
                    <input type="number" id="list-offset" value="0" min="0">
                </div>
                <div class="form-group">
                    <button class="btn btn-primary" onclick="getOutCodeList()">获取列表</button>
                </div>
            </div>
        </div>

        <!-- 出码申请详情 -->
        <div class="section">
            <h2>出码申请详情</h2>
            <div class="form-inline">
                <div class="form-group">
                    <label for="detail-id">申请ID</label>
                    <input type="number" id="detail-id" placeholder="请输入申请ID">
                </div>
                <div class="form-group">
                    <button class="btn btn-primary" onclick="getOutCodeDetail()">获取详情</button>
                </div>
            </div>
        </div>

        <!-- 消息显示 -->
        <div class="section">
            <h2>消息记录</h2>
            <div class="messages" id="messages"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let isLoggedIn = false;
        let token = '';

        // 设置默认账期为今天（YYYYMMDD格式）
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        document.getElementById('out-account-period').value = year + month + day;

        // 货币类型复选框事件
        document.getElementById('enable-chips').addEventListener('change', function() {
            document.getElementById('chips-amount-group').style.display = this.checked ? 'block' : 'none';
        });
        document.getElementById('enable-cash').addEventListener('change', function() {
            document.getElementById('cash-amount-group').style.display = this.checked ? 'block' : 'none';
        });
        document.getElementById('enable-ucode').addEventListener('change', function() {
            document.getElementById('ucode-amount-group').style.display = this.checked ? 'block' : 'none';
        });

        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                addMessage('已经连接到服务器', 'error');
                return;
            }

            ws = new WebSocket('ws://************:8080/ws');
            
            ws.onopen = function(event) {
                updateConnectionStatus(true);
                addMessage('WebSocket连接已建立', 'success');
            };

            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                handleMessage(message);
            };

            ws.onerror = function(error) {
                addMessage('WebSocket错误: ' + JSON.stringify(error), 'error');
            };

            ws.onclose = function(event) {
                updateConnectionStatus(false);
                addMessage('WebSocket连接已关闭', 'error');
                isLoggedIn = false;
                token = '';
                document.getElementById('user-info-display').innerHTML = '';
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        function updateConnectionStatus(connected) {
            const indicator = document.getElementById('status-indicator');
            const status = document.getElementById('connection-status');
            
            if (connected) {
                indicator.className = 'status-indicator status-connected';
                status.textContent = '已连接';
            } else {
                indicator.className = 'status-indicator status-disconnected';
                status.textContent = '未连接';
                document.getElementById('client-id').textContent = '-';
            }
        }

        function login() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('请先连接到服务器', 'error');
                return;
            }

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                addMessage('请输入用户名和密码', 'error');
                return;
            }

            const message = {
                type: 'login',
                data: {
                    username: username,
                    password: password
                }
            };

            sendMessage(message);
        }

        function applyOutCode() {
            if (!isLoggedIn) {
                addMessage('请先登录', 'error');
                return;
            }

            const operationType = parseInt(document.getElementById('operation-type').value);
            const accountPeriod = document.getElementById('out-account-period').value;
            
            if (!accountPeriod) {
                addMessage('请填写账期', 'error');
                return;
            }

            // 验证账期格式 (YYYYMMDD)
            if (!/^\d{8}$/.test(accountPeriod)) {
                addMessage('账期格式错误，请使用YYYYMMDD格式，例如: ********', 'error');
                return;
            }

            // 构建金额数组
            const amounts = [];
            
            if (document.getElementById('enable-chips').checked) {
                const chipsAmount = parseFloat(document.getElementById('chips-amount').value);
                if (chipsAmount > 0) {
                    amounts.push({
                        currency_type: 1,
                        total_amount: chipsAmount
                    });
                }
            }
            
            if (document.getElementById('enable-cash').checked) {
                const cashAmount = parseFloat(document.getElementById('cash-amount').value);
                if (cashAmount > 0) {
                    amounts.push({
                        currency_type: 2,
                        total_amount: cashAmount
                    });
                }
            }
            
            if (document.getElementById('enable-ucode').checked) {
                const ucodeAmount = parseFloat(document.getElementById('ucode-amount').value);
                if (ucodeAmount > 0) {
                    amounts.push({
                        currency_type: 3,
                        total_amount: ucodeAmount
                    });
                }
            }

            if (amounts.length === 0) {
                addMessage('请至少选择一种货币类型并设置金额', 'error');
                return;
            }

            const operationTypeName = operationType === 1 ? '出码' : '加彩';
            
            const message = {
                type: 'apply_out_code',
                data: {
                    operation_type: operationType,
                    account_period: accountPeriod,
                    amounts: amounts
                }
            };

            addMessage(`正在提交${operationTypeName}申请...`, 'info');
            sendMessage(message);
        }

        function getOutCodeList() {
            if (!isLoggedIn) {
                addMessage('请先登录', 'error');
                return;
            }

            const tableId = parseInt(document.getElementById('list-table-id').value) || 0;
            const status = parseInt(document.getElementById('list-status').value) || 0;
            const limit = parseInt(document.getElementById('list-limit').value) || 20;
            const offset = parseInt(document.getElementById('list-offset').value) || 0;

            const message = {
                type: 'get_out_code_list',
                data: {
                    table_id: tableId,
                    status: status,
                    limit: limit,
                    offset: offset
                }
            };

            sendMessage(message);
        }

        function getOutCodeDetail() {
            if (!isLoggedIn) {
                addMessage('请先登录', 'error');
                return;
            }

            const id = parseInt(document.getElementById('detail-id').value);
            if (!id) {
                addMessage('请输入申请ID', 'error');
                return;
            }

            const message = {
                type: 'get_out_code_detail',
                data: {
                    id: id
                }
            };

            sendMessage(message);
        }

        function sendMessage(message) {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('WebSocket未连接', 'error');
                return;
            }

            ws.send(JSON.stringify(message));
            addMessage('发送: ' + JSON.stringify(message, null, 2), 'send');
        }

        function handleMessage(message) {
            addMessage('接收: ' + JSON.stringify(message, null, 2), 'receive');

            switch(message.type) {
                case 'login_success':
                    isLoggedIn = true;
                    token = message.data.token;
                    document.getElementById('client-id').textContent = message.to || 'unknown';
                    displayUserInfo(message.data.user_info);
                    break;
                    
                case 'login_error':
                    addMessage('登录失败: ' + message.data.error, 'error');
                    break;
                    
                case 'apply_out_code_success':
                    // 根据操作类型显示不同的成功消息
                    const records = message.data.out_code_list;
                    if (records && records.length > 0) {
                        const operationType = records[0].type;
                        if (operationType === 1) {
                            addMessage('批量出码申请成功! 桌台状态已更新为等待洗牌', 'success');
                        } else if (operationType === 3) {
                            addMessage('批量加彩申请成功! 申请状态为申请中，等待审核', 'success');
                        }
                    } else {
                        addMessage('申请提交成功!', 'success');
                    }
                    console.log('申请结果:', message.data.out_code_list);
                    break;
                    
                case 'apply_out_code_error':
                    addMessage('申请失败: ' + message.data.error, 'error');
                    // 如果是唯一性冲突错误，提供更友好的提示
                    if (message.data.error.includes('已存在') && message.data.error.includes('出码记录')) {
                        addMessage('提示: 请检查是否已经为该桌台和账期申请过相同货币类型的出码', 'error');
                    }
                    break;
                    
                case 'get_out_code_list_success':
                    addMessage('获取出码申请列表成功, 共 ' + message.data.total + ' 条记录', 'success');
                    console.log('出码申请列表:', message.data.list);
                    break;
                    
                case 'get_out_code_list_error':
                    addMessage('获取出码申请列表失败: ' + message.data.error, 'error');
                    break;
                    
                case 'get_out_code_detail_success':
                    addMessage('获取出码申请详情成功', 'success');
                    console.log('出码申请详情:', message.data.out_code_info);
                    break;
                    
                case 'get_out_code_detail_error':
                    addMessage('获取出码申请详情失败: ' + message.data.error, 'error');
                    break;
            }
        }

        function displayUserInfo(userInfo) {
            const html = `
                <div class="user-info">
                    <h3>用户信息</h3>
                    <p><strong>用户名:</strong> ${userInfo.username}</p>
                    <p><strong>真实姓名:</strong> ${userInfo.realname}</p>
                    <p><strong>昵称:</strong> ${userInfo.nickname}</p>
                    <p><strong>状态:</strong> ${userInfo.status_name}</p>
                    <p><strong>登录时间:</strong> ${new Date(userInfo.login_time * 1000).toLocaleString()}</p>
                </div>
            `;
            document.getElementById('user-info-display').innerHTML = html;
        }

        function addMessage(content, type) {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message message-' + type;
            
            const timestamp = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `<span class="timestamp">${timestamp}</span>${content}`;
            
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        // 页面加载完成后自动连接
        window.onload = function() {
            connect();
        };
    </script>
</body>
</html> 