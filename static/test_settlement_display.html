<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收盘展示数据测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, button {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-right: 10px;
            width: auto;
            padding: 10px 20px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .currency-chip { background-color: #007bff; color: white; }
        .currency-cash { background-color: #28a745; color: white; }
        .currency-ucode { background-color: #ffc107; color: #212529; }
        .currency-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .form-row {
            display: flex;
            gap: 10px;
            align-items: end;
        }
        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div style="margin-bottom: 20px;">
            <a href="index.html" style="padding: 8px 16px; background-color: #6c757d; color: white; text-decoration: none; border-radius: 4px;">← 返回主页</a>
        </div>
        
        <h1>收盘展示数据测试</h1>
        
        <div class="form-group">
            <label for="serverUrl">服务器地址:</label>
            <input type="text" id="serverUrl" value="ws://192.168.1.44:8080/ws" placeholder="WebSocket服务器地址">
        </div>
        
        <div class="form-group">
            <button onclick="connect()">连接</button>
            <button onclick="disconnect()">断开连接</button>
            <button onclick="login()">登录</button>
        </div>
        
        <div id="status" class="status disconnected">未连接</div>
    </div>

    <div class="container">
        <h2>获取收盘展示数据</h2>
        
        <div class="form-row">
            <div class="form-group">
                <label for="accountPeriod">账期 (YYYYMMDD):</label>
                <input type="text" id="accountPeriod" value="********" placeholder="例如: ********">
            </div>
            <div class="form-group">
                <button onclick="getSettlementDisplay()">获取收盘展示数据</button>
            </div>
        </div>
        
        <div id="settlementData" style="margin-top: 20px;"></div>
    </div>

    <div class="container">
        <h2>消息日志</h2>
        <div id="log" class="log"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <script>
        let ws = null;
        let isConnected = false;

        function updateStatus(message, connected) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = connected ? 'status connected' : 'status disconnected';
            isConnected = connected;
        }

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function connect() {
            const serverUrl = document.getElementById('serverUrl').value;
            
            if (ws) {
                ws.close();
            }
            
            ws = new WebSocket(serverUrl);
            
            ws.onopen = function(event) {
                updateStatus('已连接', true);
                log('WebSocket连接已建立');
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                log(`收到消息: ${JSON.stringify(message, null, 2)}`);
                
                // 处理收盘展示数据响应
                if (message.type === 'get_settlement_display_success') {
                    displaySettlementData(message.data);
                } else if (message.type === 'get_settlement_display_error') {
                    displayError(message.data.error);
                }
            };
            
            ws.onclose = function(event) {
                updateStatus('连接已断开', false);
                log('WebSocket连接已断开');
            };
            
            ws.onerror = function(error) {
                updateStatus('连接错误', false);
                log('WebSocket错误: ' + error);
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function login() {
            if (!isConnected) {
                alert('请先连接到服务器');
                return;
            }
            
            const loginMessage = {
                type: 'login',
                data: {
                    username: 'admin',
                    password: 'admin123'
                }
            };
            
            ws.send(JSON.stringify(loginMessage));
            log('发送登录请求');
        }

        function getSettlementDisplay() {
            if (!isConnected) {
                alert('请先连接到服务器');
                return;
            }
            
            const accountPeriod = document.getElementById('accountPeriod').value;
            if (!accountPeriod) {
                alert('请输入账期');
                return;
            }
            
            const message = {
                type: 'get_settlement_display',
                data: {
                    account_period: accountPeriod
                }
            };
            
            ws.send(JSON.stringify(message));
            log(`发送获取收盘展示数据请求，账期: ${accountPeriod}`);
        }

        function displaySettlementData(data) {
            const container = document.getElementById('settlementData');
            
            if (!data.data || data.data.length === 0) {
                container.innerHTML = '<p>暂无收盘展示数据</p>';
                return;
            }
            
            let html = `
                <h3>${data.message}</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>货币类型</th>
                            <th>总额</th>
                            <th>点码与注单比对结果</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            data.data.forEach(item => {
                const currencyName = getCurrencyName(item.currency_type);
                const currencyClass = getCurrencyClass(item.currency_type);
                
                html += `
                    <tr>
                        <td>
                            <span class="currency-badge ${currencyClass}">
                                ${currencyName}
                            </span>
                        </td>
                        <td>${formatAmount(item.total_amount)}</td>
                        <td>${item.compare_result}</td>
                    </tr>
                `;
            });
            
            html += `
                    </tbody>
                </table>
            `;
            
            container.innerHTML = html;
        }

        function displayError(error) {
            const container = document.getElementById('settlementData');
            container.innerHTML = `<div style="color: red; padding: 10px; background-color: #f8d7da; border-radius: 4px;">错误: ${error}</div>`;
        }

        function getCurrencyName(type) {
            switch(type) {
                case 1: return '筹码';
                case 2: return '现金';
                case 3: return 'U码';
                default: return '未知';
            }
        }

        function getCurrencyClass(type) {
            switch(type) {
                case 1: return 'currency-chip';
                case 2: return 'currency-cash';
                case 3: return 'currency-ucode';
                default: return '';
            }
        }

        function formatAmount(amount) {
            return new Intl.NumberFormat('zh-CN', {
                style: 'decimal',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
    </script>
</body>
</html>
