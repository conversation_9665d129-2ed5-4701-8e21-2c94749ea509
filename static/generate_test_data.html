<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百家乐测试数据生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-case {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f9f9f9;
        }
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
        .bet-data, .result-data {
            background-color: #fff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .expected-result {
            background-color: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .copy-btn {
            background-color: #6c757d;
            font-size: 12px;
            padding: 5px 10px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div style="margin-bottom: 20px;">
            <a href="index.html" style="padding: 8px 16px; background-color: #6c757d; color: white; text-decoration: none; border-radius: 4px;">← 返回主页</a>
        </div>

        <h1>百家乐测试数据生成器</h1>
        
        <div class="form-group">
            <label for="serverUrl">服务器地址:</label>
            <input type="text" id="serverUrl" value="ws://localhost:8080/ws" style="width: 300px; padding: 5px;">
            <button onclick="connect()">连接</button>
            <button onclick="disconnect()">断开连接</button>
            <button onclick="login()">登录</button>
        </div>
        
        <div id="status" class="status disconnected">未连接</div>
        
        <button onclick="runAllTests()">运行所有测试</button>
        <button onclick="clearResults()">清空结果</button>
    </div>

    <div class="container">
        <h2>测试用例</h2>
        
        <!-- 测试用例1：庄赢 -->
        <div class="test-case">
            <h3>测试用例1：庄赢（客户投注庄）</h3>
            <div class="bet-data">
                <strong>下注数据：</strong>
                <pre id="bet1">庄: 1000, 闲: 0, 和: 0, 庄对: 0, 闲对: 0, 和底: 0</pre>
                <button class="copy-btn" onclick="copyBetData(1)">复制下注</button>
            </div>
            <div class="result-data">
                <strong>开奖结果：</strong> ["庄"]
                <button class="copy-btn" onclick="copyResult(['庄'])">复制结果</button>
            </div>
            <div class="expected-result">
                <strong>预期结果：</strong>
                <ul>
                    <li>总投注: 1000</li>
                    <li>总赢得: 1000 + (1000 × 0.95) = 1950</li>
                    <li>净输赢: 1950 - 1000 = 950 (客户赢950)</li>
                    <li>输口: 0</li>
                    <li>洗码量: 0</li>
                    <li>小费: 0</li>
                </ul>
            </div>
            <button onclick="runTest(1)">运行此测试</button>
        </div>

        <!-- 测试用例2：闲赢 -->
        <div class="test-case">
            <h3>测试用例2：闲赢（客户投注闲）</h3>
            <div class="bet-data">
                <strong>下注数据：</strong>
                <pre id="bet2">庄: 0, 闲: 1000, 和: 0, 庄对: 0, 闲对: 0, 和底: 0</pre>
                <button class="copy-btn" onclick="copyBetData(2)">复制下注</button>
            </div>
            <div class="result-data">
                <strong>开奖结果：</strong> ["闲"]
                <button class="copy-btn" onclick="copyResult(['闲'])">复制结果</button>
            </div>
            <div class="expected-result">
                <strong>预期结果：</strong>
                <ul>
                    <li>总投注: 1000</li>
                    <li>总赢得: 1000 + (1000 × 1.0) = 2000</li>
                    <li>净输赢: 2000 - 1000 = 1000 (客户赢1000)</li>
                    <li>输口: 0</li>
                    <li>洗码量: 0</li>
                    <li>小费: 0</li>
                </ul>
            </div>
            <button onclick="runTest(2)">运行此测试</button>
        </div>

        <!-- 测试用例3：客户输钱 -->
        <div class="test-case">
            <h3>测试用例3：庄赢（客户投注闲）</h3>
            <div class="bet-data">
                <strong>下注数据：</strong>
                <pre id="bet3">庄: 0, 闲: 1000, 和: 0, 庄对: 0, 闲对: 0, 和底: 0</pre>
                <button class="copy-btn" onclick="copyBetData(3)">复制下注</button>
            </div>
            <div class="result-data">
                <strong>开奖结果：</strong> ["庄"]
                <button class="copy-btn" onclick="copyResult(['庄'])">复制结果</button>
            </div>
            <div class="expected-result">
                <strong>预期结果：</strong>
                <ul>
                    <li>总投注: 1000</li>
                    <li>总赢得: 0 (没有中奖投注)</li>
                    <li>净输赢: 0 - 1000 = -1000 (客户输1000)</li>
                    <li>输口: 1000</li>
                    <li>洗码量: 1000</li>
                    <li>小费: 0</li>
                </ul>
            </div>
            <button onclick="runTest(3)">运行此测试</button>
        </div>

        <!-- 测试用例4：和赢 -->
        <div class="test-case">
            <h3>测试用例4：和赢（和的投注本金变小费）</h3>
            <div class="bet-data">
                <strong>下注数据：</strong>
                <pre id="bet4">庄: 0, 闲: 0, 和: 1000, 庄对: 0, 闲对: 0, 和底: 0</pre>
                <button class="copy-btn" onclick="copyBetData(4)">复制下注</button>
            </div>
            <div class="result-data">
                <strong>开奖结果：</strong> ["和"]
                <button class="copy-btn" onclick="copyResult(['和'])">复制结果</button>
            </div>
            <div class="expected-result">
                <strong>预期结果：</strong>
                <ul>
                    <li>总投注: 1000</li>
                    <li>总赢得: 1000 + (1000 × 8.0) = 9000</li>
                    <li>净输赢: 9000 - 1000 = 8000 (客户赢8000)</li>
                    <li>输口: 0</li>
                    <li>洗码量: 0</li>
                    <li>小费: 1000 (和的投注本金变小费)</li>
                </ul>
            </div>
            <button onclick="runTest(4)">运行此测试</button>
        </div>

        <!-- 测试用例5：庄对赢 -->
        <div class="test-case">
            <h3>测试用例5：庄对赢（和底变小费）</h3>
            <div class="bet-data">
                <strong>下注数据：</strong>
                <pre id="bet5">庄: 1000, 闲: 0, 和: 0, 庄对: 100, 闲对: 0, 和底: 50</pre>
                <button class="copy-btn" onclick="copyBetData(5)">复制下注</button>
            </div>
            <div class="result-data">
                <strong>开奖结果：</strong> ["庄", "庄对"]
                <button class="copy-btn" onclick="copyResult(['庄', '庄对'])">复制结果</button>
            </div>
            <div class="expected-result">
                <strong>预期结果：</strong>
                <ul>
                    <li>总投注: 1100</li>
                    <li>总赢得: (1000 + 1000×0.95) + (100 + 100×11) = 1950 + 1200 = 3150</li>
                    <li>净输赢: 3150 - 1100 = 2050 (客户赢2050)</li>
                    <li>输口: 0</li>
                    <li>洗码量: 0</li>
                    <li>小费: 50 (和底变小费)</li>
                </ul>
            </div>
            <button onclick="runTest(5)">运行此测试</button>
        </div>

        <!-- 测试用例6：复合投注输钱 -->
        <div class="test-case">
            <h3>测试用例6：复合投注（客户输钱）</h3>
            <div class="bet-data">
                <strong>下注数据：</strong>
                <pre id="bet6">庄: 500, 闲: 800, 和: 200, 庄对: 100, 闲对: 100, 和底: 30</pre>
                <button class="copy-btn" onclick="copyBetData(6)">复制下注</button>
            </div>
            <div class="result-data">
                <strong>开奖结果：</strong> ["庄"]
                <button class="copy-btn" onclick="copyResult(['庄'])">复制结果</button>
            </div>
            <div class="expected-result">
                <strong>预期结果：</strong>
                <ul>
                    <li>总投注: 1700</li>
                    <li>总赢得: 500 + (500×0.95) = 975</li>
                    <li>净输赢: 975 - 1700 = -725 (客户输725)</li>
                    <li>输口: 725</li>
                    <li>洗码量: 725</li>
                    <li>小费: 0</li>
                </ul>
            </div>
            <button onclick="runTest(6)">运行此测试</button>
        </div>
    </div>

    <div class="container">
        <h2>测试结果</h2>
        <div id="testResults" style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; min-height: 200px; font-family: monospace; font-size: 12px; white-space: pre-wrap;"></div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        let currentTestCase = 1;

        // 测试数据定义
        const testCases = {
            1: {
                bet: {banker_amount: 1000, player_amount: 0, tie_amount: 0, banker_pair_amount: 0, player_pair_amount: 0, amount_bottom: 0},
                result: ["庄"]
            },
            2: {
                bet: {banker_amount: 0, player_amount: 1000, tie_amount: 0, banker_pair_amount: 0, player_pair_amount: 0, amount_bottom: 0},
                result: ["闲"]
            },
            3: {
                bet: {banker_amount: 0, player_amount: 1000, tie_amount: 0, banker_pair_amount: 0, player_pair_amount: 0, amount_bottom: 0},
                result: ["庄"]
            },
            4: {
                bet: {banker_amount: 0, player_amount: 0, tie_amount: 1000, banker_pair_amount: 0, player_pair_amount: 0, amount_bottom: 0},
                result: ["和"]
            },
            5: {
                bet: {banker_amount: 1000, player_amount: 0, tie_amount: 0, banker_pair_amount: 100, player_pair_amount: 0, amount_bottom: 50},
                result: ["庄", "庄对"]
            },
            6: {
                bet: {banker_amount: 500, player_amount: 800, tie_amount: 200, banker_pair_amount: 100, player_pair_amount: 100, amount_bottom: 30},
                result: ["庄"]
            }
        };

        function updateStatus(message, connected) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = connected ? 'status connected' : 'status disconnected';
            isConnected = connected;
        }

        function log(message) {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.textContent += `[${timestamp}] ${message}\n`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function connect() {
            const serverUrl = document.getElementById('serverUrl').value;
            
            if (ws) {
                ws.close();
            }
            
            ws = new WebSocket(serverUrl);
            
            ws.onopen = function(event) {
                updateStatus('已连接', true);
                log('WebSocket连接已建立');
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                log(`收到消息: ${JSON.stringify(message, null, 2)}`);
            };
            
            ws.onclose = function(event) {
                updateStatus('连接已断开', false);
                log('WebSocket连接已断开');
            };
            
            ws.onerror = function(error) {
                updateStatus('连接错误', false);
                log('WebSocket错误: ' + error);
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function login() {
            if (!isConnected) {
                alert('请先连接到服务器');
                return;
            }
            
            const loginMessage = {
                type: 'login',
                data: {
                    username: 'admin',
                    password: 'admin123'
                }
            };
            
            ws.send(JSON.stringify(loginMessage));
            log('发送登录请求');
        }

        function runTest(testNum) {
            if (!isConnected) {
                alert('请先连接到服务器');
                return;
            }

            const testCase = testCases[testNum];
            if (!testCase) {
                log(`测试用例${testNum}不存在`);
                return;
            }

            log(`\n=== 开始运行测试用例${testNum} ===`);
            
            // 发送下注记录
            sendBetRecord(testCase.bet, testNum);
            
            // 延迟发送结果
            setTimeout(() => {
                sendResult(testCase.result, testNum);
            }, 1000);
        }

        function sendBetRecord(betData, testNum) {
            const betRecord = {
                game_type: 1,
                account_period: "********",
                round_no: 1,
                hand_no: testNum,
                wash_code: `TEST${String(testNum).padStart(3, '0')}`,
                user_name: `测试用户${testNum}`,
                currency_type: 1,
                banker_amount: betData.banker_amount,
                player_amount: betData.player_amount,
                tie_amount: betData.tie_amount,
                banker_pair_amount: betData.banker_pair_amount,
                player_pair_amount: betData.player_pair_amount,
                lucky_6_amount: 0,
                lucky_7_amount: 0,
                amount_bottom: betData.amount_bottom
            };
            
            const message = {
                type: 'batch_bet_entry',
                data: {
                    bet_records: [betRecord]
                }
            };
            
            ws.send(JSON.stringify(message));
            log(`发送测试用例${testNum}下注记录: ${JSON.stringify(betData)}`);
        }

        function sendResult(result, testNum) {
            const message = {
                type: 'enter_result',
                data: {
                    account_period: "********",
                    round_no: 1,
                    hand_no: testNum,
                    result: result
                }
            };
            
            ws.send(JSON.stringify(message));
            log(`发送测试用例${testNum}开奖结果: ${result.join(', ')}`);
        }

        function runAllTests() {
            if (!isConnected) {
                alert('请先连接到服务器');
                return;
            }

            log('\n=== 开始运行所有测试用例 ===');
            
            for (let i = 1; i <= 6; i++) {
                setTimeout(() => {
                    runTest(i);
                }, (i - 1) * 3000); // 每个测试间隔3秒
            }
        }

        function clearResults() {
            document.getElementById('testResults').textContent = '';
        }

        function copyBetData(testNum) {
            const testCase = testCases[testNum];
            const betData = JSON.stringify(testCase.bet, null, 2);
            navigator.clipboard.writeText(betData).then(() => {
                alert('下注数据已复制到剪贴板');
            });
        }

        function copyResult(result) {
            const resultData = JSON.stringify(result);
            navigator.clipboard.writeText(resultData).then(() => {
                alert('开奖结果已复制到剪贴板');
            });
        }
    </script>
</body>
</html>
