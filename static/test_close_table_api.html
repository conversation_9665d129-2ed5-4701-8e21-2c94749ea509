<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收盘接口测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status-panel {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected {
            background-color: #28a745;
        }
        .status-disconnected {
            background-color: #dc3545;
        }
        .control-panel {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
        }
        .section h2 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }
        .form-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-group input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            background-color: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            word-wrap: break-word;
        }
        .message-send {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .message-receive {
            background-color: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }
        .message-error {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
        }
        .message-success {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .timestamp {
            color: #666;
            font-size: 11px;
            margin-right: 10px;
        }
        .result-panel {
            background-color: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
        }
        .result-panel h3 {
            margin-top: 0;
            color: #2e7d32;
        }
        .result-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .result-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #e0e0e0;
        }
        .result-item:last-child {
            border-bottom: none;
        }
        .result-label {
            font-weight: bold;
            color: #495057;
        }
        .result-value {
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>收盘接口测试工具</h1>
        
        <!-- 连接状态 -->
        <div class="status-panel">
            <h3>连接状态</h3>
            <div>
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="statusText">未连接</span>
            </div>
            <div class="control-panel">
                <button class="btn btn-primary" onclick="connect()">连接</button>
                <button class="btn btn-danger" onclick="disconnect()">断开</button>
                <button class="btn btn-success" onclick="login()">登录</button>
                <button class="btn btn-danger" onclick="logout()">登出</button>
            </div>
        </div>

        <!-- 收盘功能 -->
        <div class="section">
            <h2>收盘功能</h2>
            <p style="color: #666; font-size: 14px; margin-bottom: 15px;">
                注意：桌台ID将通过客户端IP自动获取，无需手动输入
            </p>
            <button class="btn btn-danger" onclick="closeTable()">收盘</button>
        </div>

        <!-- 收盘结果 -->
        <div class="section">
            <h2>收盘结果</h2>
            <div id="closeResult" class="result-panel" style="display: none;">
                <h3>收盘成功</h3>
                <div id="resultContent" class="result-grid">
                </div>
            </div>
        </div>

        <!-- 消息日志 -->
        <div class="section">
            <h2>消息日志</h2>
            <div id="messages" class="messages"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        let isAuthenticated = false;

        function updateStatus() {
            const indicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            
            if (isConnected) {
                indicator.className = 'status-indicator status-connected';
                statusText.textContent = isAuthenticated ? '已连接且已登录' : '已连接';
            } else {
                indicator.className = 'status-indicator status-disconnected';
                statusText.textContent = '未连接';
            }
        }

        function addMessage(message, type = 'receive') {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message message-${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `<span class="timestamp">${timestamp}</span>${message}`;
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                addMessage('WebSocket已连接', 'error');
                return;
            }

            ws = new WebSocket('ws://192.168.1.44:8080/ws');
            
            ws.onopen = function() {
                isConnected = true;
                updateStatus();
                addMessage('WebSocket连接已建立', 'success');
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                addMessage(`收到消息: ${JSON.stringify(message, null, 2)}`, 'receive');
                
                switch(message.type) {
                    case 'login_success':
                        isAuthenticated = true;
                        updateStatus();
                        addMessage('登录成功!', 'success');
                        break;
                        
                    case 'login_error':
                        addMessage('登录失败: ' + message.data.error, 'error');
                        break;
                        
                    case 'logout_success':
                        isAuthenticated = false;
                        updateStatus();
                        addMessage('登出成功!', 'success');
                        break;
                        
                    case 'close_table_success':
                        addMessage('收盘成功!', 'success');
                        displayCloseResult(message.data);
                        break;
                        
                    case 'close_table_error':
                        addMessage('收盘失败: ' + message.data.error, 'error');
                        break;
                }
            };
            
            ws.onclose = function() {
                isConnected = false;
                isAuthenticated = false;
                updateStatus();
                addMessage('WebSocket连接已关闭', 'error');
            };
            
            ws.onerror = function(error) {
                addMessage('WebSocket错误: ' + error, 'error');
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        function login() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('请先连接WebSocket', 'error');
                return;
            }

            const message = {
                type: 'login',
                data: {
                    username: 'admin',
                    password: '123456'
                }
            };
            
            ws.send(JSON.stringify(message));
            addMessage(`发送登录请求: ${JSON.stringify(message, null, 2)}`, 'send');
        }

        function logout() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('请先连接WebSocket', 'error');
                return;
            }

            const message = {
                type: 'logout'
            };
            
            ws.send(JSON.stringify(message));
            addMessage(`发送登出请求: ${JSON.stringify(message, null, 2)}`, 'send');
        }

        function closeTable() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('请先连接WebSocket', 'error');
                return;
            }

            if (!isAuthenticated) {
                addMessage('请先登录', 'error');
                return;
            }

            const message = {
                type: 'close_table',
                data: {
                    // 桌台ID将通过客户端IP自动获取，无需传递table_id参数
                }
            };
            
            ws.send(JSON.stringify(message));
            addMessage(`发送收盘请求: ${JSON.stringify(message, null, 2)}`, 'send');
        }

        function displayCloseResult(data) {
            const resultDiv = document.getElementById('closeResult');
            const contentDiv = document.getElementById('resultContent');
            
            resultDiv.style.display = 'block';
            contentDiv.innerHTML = '';
            
            if (data.tables_start) {
                const tableStart = data.tables_start;
                const fields = [
                    { label: '记录ID', key: 'id' },
                    { label: '桌台ID', key: 'table_id' },
                    { label: '账期', key: 'account_period' },
                    { label: '游戏类型', key: 'game_type_name' },
                    { label: '状态', key: 'stats_name' },
                    { label: '场次编号', key: 'shoe_no' },
                    { label: '局号编号', key: 'hand_no' },
                    { label: '创建时间', key: 'create_time' }
                ];
                
                fields.forEach(field => {
                    const item = document.createElement('div');
                    item.className = 'result-item';
                    item.innerHTML = `
                        <span class="result-label">${field.label}:</span>
                        <span class="result-value">${tableStart[field.key] || ''}</span>
                    `;
                    contentDiv.appendChild(item);
                });
            }
        }

        // 初始化状态
        updateStatus();
    </script>
</body>
</html> 