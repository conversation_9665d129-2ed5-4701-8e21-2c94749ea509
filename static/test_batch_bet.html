<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量下注接口测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .bet-record {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .bet-record h4 {
            margin-top: 0;
            color: #333;
        }
        .form-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }
        .remove-btn {
            background-color: #dc3545;
            padding: 5px 10px;
            font-size: 12px;
        }
        .remove-btn:hover {
            background-color: #c82333;
        }
        .add-btn {
            background-color: #28a745;
        }
        .add-btn:hover {
            background-color: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>批量下注接口测试</h1>
        
        <div class="form-group">
            <label for="serverUrl">服务器地址:</label>
            <input type="text" id="serverUrl" value="ws://localhost:8080/ws" placeholder="WebSocket服务器地址">
        </div>
        
        <div class="form-group">
            <button onclick="connect()">连接</button>
            <button onclick="disconnect()">断开连接</button>
            <button onclick="login()">登录</button>
        </div>
        
        <div id="status" class="status disconnected">未连接</div>
    </div>

    <div class="container">
        <h2>批量下注测试</h2>
        
        <div class="form-group">
            <button class="add-btn" onclick="addBetRecord()">添加下注记录</button>
            <button onclick="sendBatchBet()">发送批量下注</button>
            <button onclick="clearBetRecords()">清空记录</button>
        </div>
        
        <div id="betRecords">
            <!-- 下注记录将在这里动态生成 -->
        </div>
    </div>

    <div class="container">
        <h2>消息日志</h2>
        <div id="log" class="log"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        let recordCount = 0;

        function updateStatus(message, connected) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = connected ? 'status connected' : 'status disconnected';
            isConnected = connected;
        }

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function connect() {
            const serverUrl = document.getElementById('serverUrl').value;
            
            if (ws) {
                ws.close();
            }
            
            ws = new WebSocket(serverUrl);
            
            ws.onopen = function(event) {
                updateStatus('已连接', true);
                log('WebSocket连接已建立');
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                log(`收到消息: ${JSON.stringify(message, null, 2)}`);
                
                if (message.type === 'batch_bet_entry_success') {
                    log(`批量下注成功: 处理了${message.data.processed_count}条记录`);
                    if (message.data.results) {
                        message.data.results.forEach((result, index) => {
                            if (result.success) {
                                log(`  记录${index + 1}: ✓ ${result.message}`);
                            } else {
                                log(`  记录${index + 1}: ✗ ${result.message}`);
                            }
                        });
                    }
                } else if (message.type === 'batch_bet_entry_error') {
                    log(`批量下注失败: ${message.data.error}`);
                }
            };
            
            ws.onclose = function(event) {
                updateStatus('连接已断开', false);
                log('WebSocket连接已断开');
            };
            
            ws.onerror = function(error) {
                updateStatus('连接错误', false);
                log('WebSocket错误: ' + error);
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function login() {
            if (!isConnected) {
                alert('请先连接到服务器');
                return;
            }
            
            const loginMessage = {
                type: 'login',
                data: {
                    username: 'admin',
                    password: 'admin123'
                }
            };
            
            ws.send(JSON.stringify(loginMessage));
            log('发送登录请求');
        }

        function addBetRecord() {
            recordCount++;
            const container = document.getElementById('betRecords');
            
            const recordDiv = document.createElement('div');
            recordDiv.className = 'bet-record';
            recordDiv.id = `record-${recordCount}`;
            
            recordDiv.innerHTML = `
                <h4>下注记录 #${recordCount} <button class="remove-btn" onclick="removeBetRecord(${recordCount})">删除</button></h4>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>游戏类型:</label>
                        <select id="gameType-${recordCount}">
                            <option value="1">百家乐</option>
                            <option value="2">龙虎斗</option>
                            <option value="3">百家乐免佣</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>账期:</label>
                        <input type="text" id="accountPeriod-${recordCount}" value="********" placeholder="YYYYMMDD">
                    </div>
                    <div class="form-group">
                        <label>场次编号:</label>
                        <input type="number" id="roundNo-${recordCount}" value="1">
                    </div>
                    <div class="form-group">
                        <label>局号编号:</label>
                        <input type="number" id="handNo-${recordCount}" value="1">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>洗码号:</label>
                        <input type="text" id="washCode-${recordCount}" value="WC${String(recordCount).padStart(3, '0')}" placeholder="洗码号">
                    </div>
                    <div class="form-group">
                        <label>客户姓名:</label>
                        <input type="text" id="userName-${recordCount}" value="测试用户${recordCount}" placeholder="客户姓名">
                    </div>
                    <div class="form-group">
                        <label>货币类型:</label>
                        <select id="currencyType-${recordCount}">
                            <option value="1">筹码</option>
                            <option value="2">现金</option>
                            <option value="3">U码</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>庄金额:</label>
                        <input type="number" id="bankerAmount-${recordCount}" value="1000" step="0.01">
                    </div>
                    <div class="form-group">
                        <label>闲金额:</label>
                        <input type="number" id="playerAmount-${recordCount}" value="500" step="0.01">
                    </div>
                    <div class="form-group">
                        <label>和金额:</label>
                        <input type="number" id="tieAmount-${recordCount}" value="100" step="0.01">
                    </div>
                    <div class="form-group">
                        <label>庄对金额:</label>
                        <input type="number" id="bankerPairAmount-${recordCount}" value="50" step="0.01">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>闲对金额:</label>
                        <input type="number" id="playerPairAmount-${recordCount}" value="50" step="0.01">
                    </div>
                    <div class="form-group">
                        <label>幸运6金额:</label>
                        <input type="number" id="lucky6Amount-${recordCount}" value="0" step="0.01">
                    </div>
                    <div class="form-group">
                        <label>幸运7金额:</label>
                        <input type="number" id="lucky7Amount-${recordCount}" value="0" step="0.01">
                    </div>
                    <div class="form-group">
                        <label>和底:</label>
                        <input type="number" id="amountBottom-${recordCount}" value="0" step="0.01">
                    </div>
                </div>
            `;
            
            container.appendChild(recordDiv);
        }

        function removeBetRecord(recordId) {
            const recordDiv = document.getElementById(`record-${recordId}`);
            if (recordDiv) {
                recordDiv.remove();
            }
        }

        function clearBetRecords() {
            document.getElementById('betRecords').innerHTML = '';
            recordCount = 0;
        }

        function sendBatchBet() {
            if (!isConnected) {
                alert('请先连接到服务器');
                return;
            }
            
            const betRecords = [];
            const recordDivs = document.querySelectorAll('.bet-record');
            
            if (recordDivs.length === 0) {
                alert('请先添加下注记录');
                return;
            }
            
            recordDivs.forEach(recordDiv => {
                const id = recordDiv.id.split('-')[1];
                
                const betRecord = {
                    game_type: parseInt(document.getElementById(`gameType-${id}`).value),
                    account_period: document.getElementById(`accountPeriod-${id}`).value,
                    round_no: parseInt(document.getElementById(`roundNo-${id}`).value),
                    hand_no: parseInt(document.getElementById(`handNo-${id}`).value),
                    wash_code: document.getElementById(`washCode-${id}`).value,
                    user_name: document.getElementById(`userName-${id}`).value,
                    currency_type: parseInt(document.getElementById(`currencyType-${id}`).value),
                    banker_amount: parseFloat(document.getElementById(`bankerAmount-${id}`).value) || 0,
                    player_amount: parseFloat(document.getElementById(`playerAmount-${id}`).value) || 0,
                    tie_amount: parseFloat(document.getElementById(`tieAmount-${id}`).value) || 0,
                    banker_pair_amount: parseFloat(document.getElementById(`bankerPairAmount-${id}`).value) || 0,
                    player_pair_amount: parseFloat(document.getElementById(`playerPairAmount-${id}`).value) || 0,
                    lucky_6_amount: parseFloat(document.getElementById(`lucky6Amount-${id}`).value) || 0,
                    lucky_7_amount: parseFloat(document.getElementById(`lucky7Amount-${id}`).value) || 0,
                    win_result: "",
                    win_loss: 0,
                    loss: 0,
                    amount_tip: 0,
                    amount_bottom: parseFloat(document.getElementById(`amountBottom-${id}`).value) || 0,
                    wash_rate: 0,
                    wash_amount: 0,
                    wash_tip: 0
                };
                
                betRecords.push(betRecord);
            });
            
            const message = {
                type: 'batch_bet_entry',
                data: {
                    bet_records: betRecords
                }
            };
            
            ws.send(JSON.stringify(message));
            log(`发送批量下注请求，包含 ${betRecords.length} 条记录`);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 页面加载时添加一条默认记录
        window.onload = function() {
            addBetRecord();
        };
    </script>
</body>
</html>
